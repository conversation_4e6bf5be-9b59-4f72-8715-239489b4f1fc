/* Custom hover effects for Sidebar Menu */
.sidebar-menu .ant-menu-item,
.sidebar-menu .ant-menu-submenu-title {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px !important;
  margin: 4px 8px !important;
  height: 48px !important;
  line-height: 48px !important;
}

.sidebar-menu .ant-menu-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.sidebar-menu .ant-menu-item:hover::before {
  left: 100%;
}

.sidebar-menu .ant-menu-item:hover,
.sidebar-menu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #fff !important;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-menu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-menu .ant-menu-item-selected::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background: #fff;
  border-radius: 2px;
}

/* Submenu styling improvements */
.sidebar-menu .ant-menu-submenu {
  background: transparent !important;
  overflow: hidden;
}

.sidebar-menu .ant-menu-submenu .ant-menu-submenu-title {
  padding-left: 24px !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
  position: relative;
}

.sidebar-menu .ant-menu-submenu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateX(4px);
}

.sidebar-menu .ant-menu-submenu .ant-menu-item {
  margin: 2px 12px 2px 24px !important;
  border-radius: 6px !important;
  padding-left: 40px !important;
  height: 36px !important;
  line-height: 36px !important;
  font-size: 13px !important;
  background: rgba(0, 0, 0, 0.15) !important;
  border-left: 3px solid rgba(255, 255, 255, 0.4) !important;
  position: relative;
  overflow: hidden;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item:hover::before {
  transform: translateX(100%);
}

.sidebar-menu .ant-menu-submenu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  transform: translateX(6px);
  border-left: 3px solid #fff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: #fff !important;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.3) !important;
  border-left: 3px solid #fff !important;
  color: #fff !important;
  font-weight: 600;
}

/* Submenu arrow styling */
.sidebar-menu .ant-menu-submenu-arrow {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
}

.sidebar-menu .ant-menu-submenu:hover .ant-menu-submenu-arrow {
  color: #fff !important;
}

/* Submenu popup styling for collapsed state */
.sidebar-menu.ant-menu-inline-collapsed .ant-menu-submenu > .ant-menu {
  background: linear-gradient(135deg, #47c8f8 0%, #1890ff 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sidebar-menu.ant-menu-inline-collapsed
  .ant-menu-submenu
  > .ant-menu
  .ant-menu-item {
  background: rgba(255, 255, 255, 0.1) !important;
  margin: 4px 8px !important;
  border-radius: 6px !important;
  border-left: none !important;
}

.sidebar-menu.ant-menu-inline-collapsed
  .ant-menu-submenu
  > .ant-menu
  .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.02);
}

/* General submenu container styling */
.sidebar-menu .ant-menu-sub {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 0 0 8px 8px !important;
  margin-top: 4px !important;
  padding: 4px 0 !important;
}

/* Smooth transitions for submenu open/close */
.sidebar-menu .ant-menu-submenu .ant-menu-sub {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Text styling improvements */
.sidebar-menu .ant-menu-item .ant-menu-title-content,
.sidebar-menu .ant-menu-submenu-title .ant-menu-title-content {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar-menu .ant-menu-item:hover .ant-menu-title-content,
.sidebar-menu .ant-menu-submenu-title:hover .ant-menu-title-content {
  color: #fff !important;
}

/* Collapsed state improvements */
.sidebar-menu.ant-menu-inline-collapsed .ant-menu-item {
  padding-left: 24px !important;
  text-align: center;
}

.sidebar-menu.ant-menu-inline-collapsed .ant-menu-item:hover {
  transform: scale(1.05);
}
