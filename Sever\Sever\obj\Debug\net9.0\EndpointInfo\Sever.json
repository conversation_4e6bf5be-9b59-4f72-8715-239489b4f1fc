{"openapi": "3.0.1", "info": {"title": "Sever API", "description": "API cho hệ thống y tế học đường", "version": "v1"}, "paths": {"/api/admin/create-accounts": {"post": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-user-info": {"put": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/delete-user": {"delete": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-users-from-file": {"post": {"tags": ["Admin"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-school-info": {"put": {"tags": ["Admin"], "parameters": [{"name": "SchoolID", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Address", "in": "query", "schema": {"type": "string"}}, {"name": "Hotline", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Logo": {"type": "string", "format": "binary"}, "LogoGifs": {"type": "string", "format": "binary"}}}, "encoding": {"Logo": {"style": "form"}, "LogoGifs": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-all-account": {"get": {"tags": ["Admin"], "responses": {"200": {"description": "OK"}}}}, "/api/admin/search-user/{key}": {"get": {"tags": ["Admin"], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-students-from-file": {"post": {"tags": ["Admin"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/create-list-student": {"post": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudentRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudentRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateStudentRequest"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/active-account": {"put": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-student-info-by-parent": {"get": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/create-student-profile": {"post": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStudentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateStudentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateStudentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/delete-student-profile/{studentId}": {"delete": {"tags": ["Admin"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-student-info-by-parentID/{parentId}": {"get": {"tags": ["Admin"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-student-profile": {"put": {"tags": ["Admin"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"StudentID": {"type": "string"}, "StudentName": {"type": "string"}, "Class": {"type": "string"}, "StudentAvata": {"type": "string", "format": "binary"}, "RelationName": {"type": "string"}, "Nationality": {"type": "string"}, "Ethnicity": {"type": "string"}, "Birthday": {"type": "string", "format": "date-time"}, "Sex": {"type": "string"}, "Location": {"type": "string"}}}, "encoding": {"StudentID": {"style": "form"}, "StudentName": {"style": "form"}, "Class": {"style": "form"}, "StudentAvata": {"style": "form"}, "RelationName": {"style": "form"}, "Nationality": {"style": "form"}, "Ethnicity": {"style": "form"}, "Birthday": {"style": "form"}, "Sex": {"style": "form"}, "Location": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/google-login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/change-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/manager/search-student-profile/{info}": {"get": {"tags": ["Manager"], "parameters": [{"name": "info", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/manager/get-news-by-manager": {"get": {"tags": ["Manager"], "responses": {"200": {"description": "OK"}}}}, "/api/manager/create-news": {"post": {"tags": ["Manager"], "parameters": [{"name": "Title", "in": "query", "schema": {"type": "string"}}, {"name": "Summary", "in": "query", "schema": {"type": "string"}}, {"name": "Body", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/manager/update-news": {"put": {"tags": ["Manager"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"NewsID": {"type": "string"}, "Title": {"type": "string"}, "Summary": {"type": "string"}, "Body": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"NewsID": {"style": "form"}, "Title": {"style": "form"}, "Summary": {"style": "form"}, "Body": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/manager/delete-news/{id}": {"delete": {"tags": ["Manager"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/manager/generate-report": {"get": {"tags": ["Manager"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/manager/get-all-student": {"get": {"tags": ["Manager"], "responses": {"200": {"description": "OK"}}}}, "/api/news/get-all-news": {"get": {"tags": ["News"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/create": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Dosage", "MedicineName", "Quantity"], "type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "StudentID": {"type": "string"}, "Status": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "StudentID": {"style": "form"}, "Status": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/update/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}, "Status": {"type": "string"}, "StudentID": {"type": "string"}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "Image": {"style": "form"}, "Status": {"style": "form"}, "StudentID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/addImage/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}, "Status": {"type": "string"}, "StudentID": {"type": "string"}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "Image": {"style": "form"}, "Status": {"style": "form"}, "StudentID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/getByStudentId/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/getAll": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/create": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Description": {"type": "string"}, "ActionTaken": {"type": "string"}, "Notes": {"type": "string"}, "EventType": {"type": "string"}, "StudentID": {"type": "array", "items": {"type": "string"}}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"Description": {"style": "form"}, "ActionTaken": {"style": "form"}, "Notes": {"style": "form"}, "EventType": {"style": "form"}, "StudentID": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/update/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicalEventID": {"type": "string"}, "Description": {"type": "string"}, "ActionTaken": {"type": "string"}, "Notes": {"type": "string"}, "EventType": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicalEventID": {"style": "form"}, "Description": {"style": "form"}, "ActionTaken": {"style": "form"}, "Notes": {"style": "form"}, "EventType": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/addImages/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicalEventID": {"type": "string"}, "Description": {"type": "string"}, "ActionTaken": {"type": "string"}, "Notes": {"type": "string"}, "EventType": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicalEventID": {"style": "form"}, "Description": {"style": "form"}, "ActionTaken": {"style": "form"}, "Notes": {"style": "form"}, "EventType": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/getByEventId/{medicalEventId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "medicalEventId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/getByStudentId/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/getAll": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/create-health-check-up-by-class": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"classId": {"type": "string"}, "dateCheckUp": {"type": "string", "format": "date-time"}}}, "encoding": {"classId": {"style": "form"}, "dateCheckUp": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-health-check-up-by-student/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-not-response-health-check-up": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-health-check-up-by-year/{year}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "year", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/update-health-check-up": {"put": {"tags": ["Nurse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHealthCheckUp"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHealthCheckUp"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHealthCheckUp"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/create-appointment": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAppointment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAppointment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAppointment"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/update-appointment": {"put": {"tags": ["Nurse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/createByStudentID": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"VaccineID": {"type": "integer", "format": "int32"}, "Dose": {"type": "integer", "format": "int32"}, "Notes": {"type": "string"}, "VaccinatedAt": {"type": "string", "format": "date-time"}, "StudentID": {"type": "string"}, "ClassID": {"type": "string"}}}, "encoding": {"VaccineID": {"style": "form"}, "Dose": {"style": "form"}, "Notes": {"style": "form"}, "VaccinatedAt": {"style": "form"}, "StudentID": {"style": "form"}, "ClassID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/createByClassID": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"VaccineID": {"type": "integer", "format": "int32"}, "Dose": {"type": "integer", "format": "int32"}, "Notes": {"type": "string"}, "VaccinatedAt": {"type": "string", "format": "date-time"}, "StudentID": {"type": "string"}, "ClassID": {"type": "string"}}}, "encoding": {"VaccineID": {"style": "form"}, "Dose": {"style": "form"}, "Notes": {"style": "form"}, "VaccinatedAt": {"style": "form"}, "StudentID": {"style": "form"}, "ClassID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/updateByRecordID/{recordId}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "recordId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Dose": {"type": "integer", "format": "int32"}, "Notes": {"type": "string"}, "Status": {"type": "string"}, "VaccinatedAt": {"type": "string", "format": "date-time"}, "StudentID": {"type": "string"}, "VaccineID": {"type": "integer", "format": "int32"}, "VaccinatorID": {"type": "string"}}}, "encoding": {"Dose": {"style": "form"}, "Notes": {"style": "form"}, "Status": {"style": "form"}, "VaccinatedAt": {"style": "form"}, "StudentID": {"style": "form"}, "VaccineID": {"style": "form"}, "VaccinatorID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/updateAfterByRecordID/{recordId}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "recordId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"DateTime": {"type": "string", "format": "date-time"}, "Status": {"type": "string"}, "FollowUpNotes": {"type": "string"}, "FollowUpDate": {"type": "string", "format": "date-time"}, "StudentID": {"type": "string"}}}, "encoding": {"DateTime": {"style": "form"}, "Status": {"style": "form"}, "FollowUpNotes": {"style": "form"}, "FollowUpDate": {"style": "form"}, "StudentID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/getAll": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/getConfirm": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/getDenied": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/getNotResponse": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/vaccine/getByStudentId/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/healthProfile/getByStudent/{studentId}": {"get": {"tags": ["Nurse"], "parameters": [{"name": "studentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/healthProfile/update": {"put": {"tags": ["Nurse"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateHealthProfile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHealthProfile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHealthProfile"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/healthProfile/getAll": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-all-health-check-up": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/update-waiting-status-health-check-up/{Id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/nurse/get-all-appointment": {"get": {"tags": ["Nurse"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/create": {"post": {"tags": ["Parent"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Dosage", "MedicineName", "Quantity"], "type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "StudentID": {"type": "string"}, "Status": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "StudentID": {"style": "form"}, "Status": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/update": {"put": {"tags": ["Parent"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/addImage": {"put": {"tags": ["Parent"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "Notes": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "Notes": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/getByStudentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/getByParentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/event/getByStudentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/event/getByParentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/confirm-health-check-up": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/denied-health-check-up": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/HealthCheckUpResponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/confirm-appointment": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/denied-appointment": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAppointment"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/get-all-health-check-up-by-parent/{parentId}": {"get": {"tags": ["Parent"], "parameters": [{"name": "parentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/parent/vaccine/confirm": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/vaccine/denied": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VaccineReponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/vaccine/getParentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/get-student-info-by-parent": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/healthProfile/declare": {"put": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeclareHealthProfile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeclareHealthProfile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeclareHealthProfile"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/healthProfile/getByParentId": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/parent/get-appointment-by-student-id/{StudentID}": {"get": {"tags": ["Parent"], "parameters": [{"name": "StudentID", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/parent/update-student-profile": {"put": {"tags": ["Parent"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"StudentID": {"type": "string"}, "StudentName": {"type": "string"}, "Class": {"type": "string"}, "StudentAvata": {"type": "string", "format": "binary"}, "RelationName": {"type": "string"}, "Nationality": {"type": "string"}, "Ethnicity": {"type": "string"}, "Birthday": {"type": "string", "format": "date-time"}, "Sex": {"type": "string"}, "Location": {"type": "string"}}}, "encoding": {"StudentID": {"style": "form"}, "StudentName": {"style": "form"}, "Class": {"style": "form"}, "StudentAvata": {"style": "form"}, "RelationName": {"style": "form"}, "Nationality": {"style": "form"}, "Ethnicity": {"style": "form"}, "Birthday": {"style": "form"}, "Sex": {"style": "form"}, "Location": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/school/get-school-info": {"get": {"tags": ["School"], "responses": {"200": {"description": "OK"}}}}, "/api/school/get-all-news": {"get": {"tags": ["School"], "responses": {"200": {"description": "OK"}}}}, "/api/user/get-user-info": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/user/update-user-info": {"put": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/user/get-notify-by-user-id": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/Values/check-token": {"get": {"tags": ["Values"], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"ChangePasswordRequest": {"type": "object", "properties": {"oldPass": {"type": "string", "nullable": true}, "newPass": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateAppointment": {"type": "object", "properties": {"dateTime": {"type": "string", "format": "date-time"}, "location": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "healthCheckUpID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateStudentRequest": {"type": "object", "properties": {"studentName": {"type": "string", "nullable": true}, "class": {"type": "string", "nullable": true}, "studentAvata": {"type": "string", "format": "binary", "nullable": true}, "relationName": {"type": "string", "nullable": true}, "nationality": {"type": "string", "nullable": true}, "ethnicity": {"type": "string", "nullable": true}, "birthday": {"type": "string", "format": "date-time", "nullable": true}, "sex": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "parentUserName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "roleName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeclareHealthProfile": {"type": "object", "properties": {"studentID": {"type": "string", "nullable": true}, "allergyHistory": {"type": "string", "nullable": true}, "chronicDiseases": {"type": "string", "nullable": true}, "pastSurgeries": {"type": "integer", "format": "int32", "nullable": true}, "surgicalCause": {"type": "string", "nullable": true}, "disabilities": {"type": "string", "nullable": true}, "height": {"type": "number", "format": "float", "nullable": true}, "weight": {"type": "number", "format": "float", "nullable": true}, "visionLeft": {"type": "integer", "format": "int32", "nullable": true}, "visionRight": {"type": "integer", "format": "int32", "nullable": true}, "toothDecay": {"type": "string", "nullable": true}, "otheHealthIssues": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeleteUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GoogleLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "HealthCheckUpResponse": {"type": "object", "properties": {"heathCheckUpID": {"type": "string", "nullable": true}, "resson": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateAppointment": {"type": "object", "properties": {"appointmentID": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateHealthCheckUp": {"type": "object", "properties": {"healthCheckId": {"type": "string", "nullable": true}, "height": {"type": "number", "format": "float", "nullable": true}, "weight": {"type": "number", "format": "float", "nullable": true}, "bmi": {"type": "number", "format": "float", "nullable": true}, "visionLeft": {"type": "integer", "format": "int32", "nullable": true}, "visionRight": {"type": "integer", "format": "int32", "nullable": true}, "bloodPressure": {"type": "number", "format": "float", "nullable": true}, "dental": {"type": "string", "nullable": true}, "skin": {"type": "string", "nullable": true}, "hearing": {"type": "string", "nullable": true}, "respiration": {"type": "string", "nullable": true}, "ardiovascular": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateHealthProfile": {"required": ["studentID"], "type": "object", "properties": {"studentID": {"minLength": 1, "type": "string"}, "allergyHistory": {"type": "string", "nullable": true}, "chronicDiseases": {"type": "string", "nullable": true}, "pastSurgeries": {"type": "integer", "format": "int32", "nullable": true}, "surgicalCause": {"type": "string", "nullable": true}, "disabilities": {"type": "string", "nullable": true}, "height": {"type": "number", "format": "float", "nullable": true}, "weight": {"type": "number", "format": "float", "nullable": true}, "visionLeft": {"type": "integer", "format": "int32", "nullable": true}, "visionRight": {"type": "integer", "format": "int32", "nullable": true}, "toothDecay": {"type": "string", "nullable": true}, "otheHealthIssues": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "VaccineReponse": {"type": "object", "properties": {"recordID": {"type": "string", "nullable": true}, "dose": {"type": "integer", "format": "int32"}, "dateTime": {"type": "string", "format": "date-time"}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "vaccinatedAt": {"type": "string", "format": "date-time", "nullable": true}, "followUpNotes": {"type": "string", "nullable": true}, "followUpDate": {"type": "string", "format": "date-time", "nullable": true}, "studentID": {"type": "string", "nullable": true}, "parentID": {"type": "string", "nullable": true}, "nurseID": {"type": "string", "nullable": true}, "vaccineID": {"type": "integer", "format": "int32"}, "vaccinatorID": {"type": "string", "nullable": true}, "class": {"type": "string", "nullable": true}, "studentName": {"type": "string", "nullable": true}, "vaccinatorName": {"type": "string", "nullable": true}, "vaccineName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "<PERSON><PERSON><PERSON><PERSON> 'Bearer <token>' vào đây", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}