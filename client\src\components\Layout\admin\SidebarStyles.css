/* Logo styling */
.sidebar-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px; /* bạn có thể điều chỉnh */
  padding: 16px 0;
}

.sidebar-logo img {
  height: 40px;
  transition: transform 0.3s ease;
  display: block;
}
.sidebar-logo img:hover {
  transform: scale(1.1);
}

/* Custom hover effects for Sidebar Menu */
.sidebar-menu .ant-menu-item,
.sidebar-menu .ant-menu-submenu-title {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 8px !important;
  margin: 4px 8px !important;
  height: 48px !important;
  line-height: 48px !important;
}

.sidebar-menu .ant-menu-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.sidebar-menu .ant-menu-item:hover::before {
  left: 100%;
}

.sidebar-menu .ant-menu-item:hover,
.sidebar-menu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #fff !important;
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-menu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #fff !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-menu .ant-menu-item-selected::after {
  content: "";
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 24px;
  background: #fff;
  border-radius: 2px;
}

/* Submenu styling improvements */
.sidebar-menu .ant-menu-submenu {
  background: transparent !important;
  overflow: hidden;
}

.sidebar-menu .ant-menu-submenu .ant-menu-submenu-title {
  padding-left: 24px !important;
  border-radius: 8px !important;
  margin: 4px 8px !important;
  position: relative;
}

.sidebar-menu .ant-menu-submenu .ant-menu-submenu-title:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  transform: translateX(4px);
}

.sidebar-menu .ant-menu-submenu .ant-menu-item {
  margin: 2px 12px 2px 24px !important;
  border-radius: 6px !important;
  padding-left: 40px !important;
  height: 36px !important;
  line-height: 36px !important;
  font-size: 13px !important;
  background: rgba(0, 0, 0, 0.15) !important;
  border-left: 3px solid rgba(255, 255, 255, 0.4) !important;
  position: relative;
  overflow: hidden;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item:hover::before {
  transform: translateX(100%);
}

.sidebar-menu .ant-menu-submenu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  transform: translateX(6px);
  border-left: 3px solid #fff !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: #fff !important;
}

.sidebar-menu .ant-menu-submenu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.3) !important;
  border-left: 3px solid #fff !important;
  color: #fff !important;
  font-weight: 600;
}

/* Submenu arrow styling */
.sidebar-menu .ant-menu-submenu-arrow {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.3s ease;
}

.sidebar-menu .ant-menu-submenu:hover .ant-menu-submenu-arrow {
  color: #fff !important;
}

/* Submenu popup styling for collapsed state */
.sidebar-menu.ant-menu-inline-collapsed .ant-menu-submenu > .ant-menu {
  background: linear-gradient(135deg, #47c8f8 0%, #1890ff 100%) !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.sidebar-menu.ant-menu-inline-collapsed
  .ant-menu-submenu
  > .ant-menu
  .ant-menu-item {
  background: rgba(255, 255, 255, 0.1) !important;
  margin: 4px 8px !important;
  border-radius: 6px !important;
  border-left: none !important;
}

.sidebar-menu.ant-menu-inline-collapsed
  .ant-menu-submenu
  > .ant-menu
  .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.02);
}

/* General submenu container styling */
.sidebar-menu .ant-menu-sub {
  background: rgba(0, 0, 0, 0.05) !important;
  border-radius: 0 0 8px 8px !important;
  margin-top: 4px !important;
  padding: 4px 0 !important;
}

/* Smooth transitions for submenu open/close */
.sidebar-menu .ant-menu-submenu .ant-menu-sub {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Text styling improvements */
.sidebar-menu .ant-menu-item .ant-menu-title-content,
.sidebar-menu .ant-menu-submenu-title .ant-menu-title-content {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 500;
  transition: color 0.3s ease;
}

.sidebar-menu .ant-menu-item:hover .ant-menu-title-content,
.sidebar-menu .ant-menu-submenu-title:hover .ant-menu-title-content {
  color: #fff !important;
}

/* Collapsed state improvements */
.sidebar-menu.ant-menu-inline-collapsed .ant-menu-item {
  padding-left: 24px !important;
  text-align: center;
}

.sidebar-menu.ant-menu-inline-collapsed .ant-menu-item:hover {
  transform: scale(1.05);
}
.ant-layout-sider {
  background: linear-gradient(135deg, #47c8f8 0%, #1890ff 100%) !important;
}

/* Nếu bạn có class sidebar thì vẫn nên thêm */
/* Biến màu nền gradient chính */
:root {
  --sidebar-gradient: linear-gradient(135deg, #47c8f8 0%, #1890ff 100%);
}

/* Sidebar container */
.sidebar {
  background: var(--sidebar-gradient) !important;
  min-height: 100vh;
}

/* Menu tổng thể */
ul.ant-menu {
  background: transparent !important; /* Để nó nhận nền từ .sidebar */
  box-shadow: none !important;
}

/* Menu item riêng */
.ant-menu-item {
  background: transparent !important;
  color: white !important;
}

/* Hover + selected item vẫn giữ nền gradient nhẹ hoặc hiệu ứng khác */
.ant-menu-item:hover,
.ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.15) !important;
  color: #fff !important;
}
.sidebar {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #47c8f8 0%, #1890ff 100%) !important;
  width: 200px;
}

.sidebar-logo {
  flex-shrink: 0;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px 0;
  background: inherit;
}

.sidebar-logo img {
  height: 40px;
  transition: transform 0.3s ease;
}

.sidebar-logo img:hover {
  transform: scale(1.1);
}

.sidebar-menu-wrapper {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 16px;
}

/* Menu item styling (rút gọn cho ví dụ, có thể giữ lại bản đầy đủ bạn gửi nếu muốn) */
.sidebar-menu .ant-menu-item {
  transition: all 0.3s ease;
  margin: 4px 8px;
  border-radius: 8px;
  height: 48px;
  line-height: 48px;
}

.sidebar-menu .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(4px);
  color: #fff;
}

.sidebar-menu .ant-menu-item-selected {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-weight: 600;
  border-radius: 8px;
}
.sidebar {
  overflow-x: hidden; /* Ẩn thanh cuộn ngang */
}
.sidebar {
  overflow-y: hidden; /* Ẩn thanh cuộn ngang */
}

.sidebar-menu-wrapper {
  overflow-x: hidden; /* Ngăn cuộn ngang menu */
}