﻿using System.Net.Mail;

namespace Sever.Service
{
    public interface IEmailService
    {
        Task SendEmailAsync(string email, string subject, string message);
    }

    public class EmailSevice : IEmailService
    {
        public Task SendEmailAsync(string email, string subject, string message)
        {
            var client = new SmtpClient("smtp.gmail.com", 587)
            {
                EnableSsl = true,
                UseDefaultCredentials = false,
                Credentials = new System.Net.NetworkCredential("<EMAIL>", "mxsybfvgfngtuhfe")
            };

            var mail = new MailMessage
            {
                From = new MailAddress("<EMAIL>"),
                Subject = subject,
                Body = message,
                IsBodyHtml = true
            };

            mail.To.Add(email);

            return client.SendMailAsync(mail);
        }
    }
}
