﻿using Microsoft.EntityFrameworkCore;
using Sever.Context;
using Sever.DTO.MedicalEvent;
using Sever.Model;
using Sever.Utilities;
using System.Threading.Tasks;

namespace Sever.Repository.Interfaces
{
    public interface IMedicalEventRepository
    {
        Task<List<MedicalEvent>> GetAllMedicialEventAsync();
        Task<MedicalEvent> CreateMedicalEvent(MedicalEvent medicalEvent);
        Task CreateMedicalEventDetails(IEnumerable<MedicalEventDetail> details);
        Task<MedicalEvent> GetMedicalEventById(string medicalEventId);
        Task<List<MedicalEvent>> GetMedicalEventsByParentIdAsync(string studentId);
        Task<MedicalEvent> GetMedicalEventByIdAsync(string medicalEventId);
        Task UpdateMedicalEvent(MedicalEvent medicalEvent);
        Task<string> GetCurrentMedicialEventID();
        Task<List<MedicalEvent>> GetMedicalEventByStudentIdAsync(string studentId);
        Task<int> TotalMedicalEvent(DateTime fromDate, DateTime toDate);
        Task<int> CountEmergency(DateTime fromDate, DateTime toDate);
        Task<int> CountAccident(DateTime fromDate, DateTime toDate);

        Task<int> CountIllness(DateTime fromDate, DateTime toDate);
        Task<int> CountInjury(DateTime fromDate, DateTime toDate);
        Task<int> CountOther(DateTime fromDate, DateTime toDate);
    }
    public class MedicalEventRepository : IMedicalEventRepository
    {
        private readonly DataContext _context;

        public MedicalEventRepository(DataContext context)
        {
            _context = context;
        }

        public async Task<MedicalEvent> CreateMedicalEvent(MedicalEvent medicalEvent)
        {
            _context.MedicalEvent.Add(medicalEvent);
            await _context.SaveChangesAsync();
            return medicalEvent;
        }

        public async Task CreateMedicalEventDetails(IEnumerable<MedicalEventDetail> details)
        {
            _context.MedicalEventDetail.AddRange(details);
            await _context.SaveChangesAsync();
        }

        public async Task<MedicalEvent> GetMedicalEventById(string medicalEventId)
        {
            return await _context.MedicalEvent
                .Include(m => m.MedicalEventDetail)
                .Include(m => m.File)
                .FirstOrDefaultAsync(m => m.MedicalEventID == medicalEventId);
        }
        public async Task<List<MedicalEvent>> GetMedicalEventsByParentIdAsync(string studentId)
        {
            var studentIds = await _context.StudentProfile
                .Select(sp => sp.StudentID)
                .ToListAsync();

            if (!studentIds.Any()) return new List<MedicalEvent>();

            var medicalEvents = await _context.MedicalEvent
                .Include(e => e.MedicalEventDetail)
                .Include(e => e.File)
                .Where(e => e.MedicalEventDetail.Any(d => studentIds.Contains(d.StudentID)))
                .ToListAsync();

            return medicalEvents;
        }
        public async Task<MedicalEvent> GetMedicalEventByIdAsync(string medicalEventId)
        {
            return await _context.MedicalEvent
                .Include(m => m.File)
                .Include(m => m.MedicalEventDetail)     
                    .ThenInclude(d => d.StudentProfile)         
                .FirstOrDefaultAsync(m => m.MedicalEventID == medicalEventId);
        }

        public async Task UpdateMedicalEvent(MedicalEvent medicalEvent)
        {
            _context.MedicalEvent.Update(medicalEvent);
            await _context.SaveChangesAsync();
        }

        public async Task<string> GetCurrentMedicialEventID()
        {
            var crurrentMedicine = await _context.MedicalEvent.OrderByDescending(n => n.MedicalEventID).FirstOrDefaultAsync();
            if (crurrentMedicine == null)
            {
                return "ME0001";
            }
            string result = GenerateID.GenerateNextId(crurrentMedicine.MedicalEventID, "ME", 4);
            return result;

        }
        public async Task<List<MedicalEvent>> GetMedicalEventByStudentIdAsync(string studentId)
        {
            return await _context.MedicalEvent
               .Include(m => m.File)
               .Include(m => m.MedicalEventDetail)
                    .ThenInclude(d => d.StudentProfile)
               .Where(m => m.MedicalEventDetail.Any(d => d.StudentID == studentId))
               .ToListAsync();
        } 

        public async Task<List<StudentProfile>> GetStudentsByParentIdAsync(string parentId)
        {
            return await _context.StudentProfile
                                 .Where(s => s.ParentID == parentId)
                                 .ToListAsync();
        }

        public async Task<int> TotalMedicalEvent(DateTime fromDate, DateTime toDate)
        {
            var count = await _context.MedicalEvent
                .Where(m => m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
            return count;
        }
        public async Task<int> CountEmergency(DateTime fromDate, DateTime toDate)
        {
            return await _context.MedicalEvent
                .Where(m => m.EventType == "Cấp cứu" && m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
        }
        public async Task<int> CountAccident(DateTime fromDate, DateTime toDate)
        {
            return await _context.MedicalEvent
                .Where(m => m.EventType == "Tai nạn" && m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
        }
        public async Task<int> CountIllness(DateTime fromDate, DateTime toDate)
        {
            return await _context.MedicalEvent
                .Where(m => m.EventType == "Bệnh tật" && m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
        }
        public async Task<List<MedicalEvent>> GetAllMedicialEventAsync()
        {
            return await _context.MedicalEvent
               .Include(m => m.File)
               .Include(e => e.MedicalEventDetail)
                    .ThenInclude(d => d.StudentProfile)
               .ToListAsync();
        }

        public async Task<int> CountInjury(DateTime fromDate, DateTime toDate)
        {
            return await _context.MedicalEvent
                .Where(m => m.EventType == "Chấn thương" && m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
        }
        public async Task<int> CountOther(DateTime fromDate, DateTime toDate)
        {
            return await _context.MedicalEvent
                .Where(m => m.EventType != "Chấn thương" &&
                            m.EventType != "Bệnh tật" &&
                            m.EventType != "Tai nạn" &&
                            m.EventType != "Cấp cứu" &&
                            m.EventDateTime >= fromDate && m.EventDateTime <= toDate)
                .CountAsync();
        }

    }
}
