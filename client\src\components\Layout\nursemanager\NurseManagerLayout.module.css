.layout {
  min-height: 100vh;
}

.siteLayout {
  transition: margin-left 0.2s;
}

.siteLayoutCollapsed {
  margin-left: 80px; 
}

.siteLayoutExpanded {
  margin-left: 200px; /* Width of expanded sidebar */
}

.header {
  padding: 0 24px;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 1;
  height: 64px; /* Set a fixed height for the header */
}


.headerLeft {
  display: flex;
  align-items: center;
  flex-grow: 0; /* Prevent headerLeft from growing */
}

.headerRight {
  display: flex;
  align-items: center;
  flex-grow: 0; /* Prevent headerRight from growing */
}

.trigger {
  padding: 0 12px;
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #fafafa; /* This hover color might need adjustment based on background */
  opacity: 0.1;
}

/* Styles for the logo */
.logoContainer {
  height: 64px; /* Match header height */
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  max-height: 100%;
  object-fit: contain;
}

/* Styles for the search bar */
.searchBar {
  width: 250px !important; /* Adjust width as needed */
  margin-left: 16px;
}

.searchBar .ant-input-search-button {
  border-radius: 0 4px 4px 0 !important;
}

/* Styles for main navigation */
.mainNav {
  display: flex;
  align-items: center;
  gap: 24px; /* Space between links */
  flex-grow: 1; /* Allow navigation to take available space */
  justify-content: center; /* Center navigation links */
}

.navLink {
  color: rgba(0, 0, 0, 0.85); /* Default link color */
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navLink:hover {
  color: #1890ff; /* Hover color */
}

.notificationButton {
  font-size: 18px; /* Increased size */
  color: rgba(0, 0, 0, 0.65); /* Default color */
}

.notificationButton:hover {
  color: #1890ff; /* Hover color */
}

.userInfo {
  cursor: pointer;
  padding: 0 12px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.userInfo:hover {
  background: rgba(0, 0, 0, 0.025);
}

.userName {
  margin-left: 8px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.content {
  margin: 24px;
  min-height: 280px;
  background: #fff;
  border-radius: 4px;
  padding: 24px;
}

.contentWrapper {
  height: 100%;
}

/* Sidebar and menu styles - mostly keeping existing */
.sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  transition: width 0.2s;
}

.sider :global(.ant-layout-sider-children) {
  background: #47c8f8 !important;
  background-color: #47c8f8 !important;
}

.sider :global(.ant-menu-dark) {
  background: #47c8f8 !important;
  background-color: #47c8f8 !important;
}

.sider :global(.ant-menu-dark .ant-menu-item),
.sider :global(.ant-menu-dark .ant-menu-submenu-title) {
  background: #47c8f8 !important;
  background-color: #47c8f8 !important;
}

.sider :global(.ant-menu-dark .ant-menu-item-selected) {
  background: #47c8f8 !important;
  background-color: #47c8f8 !important;
}

.sider :global(.ant-menu-dark .ant-menu-item:hover),
.sider :global(.ant-menu-dark .ant-menu-submenu-title:hover) {
  background: rgba(255, 255, 255, 0.08) !important;
  background-color: rgba(255, 255, 255, 0.08) !important;
}

.sider.ant-layout-sider .ant-menu {
  background-color: transparent !important;
  border-right: none !important;
}

.sider.ant-layout-sider .ant-menu-dark .ant-menu-item,
.sider.ant-layout-sider .ant-menu-dark .ant-menu-submenu-title {
  color: rgba(255, 255, 255, 0.65) !important;
  margin: 0 !important;
  border-radius: 0 !important;
  height: 48px !important;
  line-height: 48px !important;
}

.sider.ant-layout-sider .ant-menu-dark .ant-menu-item-selected,
.sider.ant-layout-sider .ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff !important;
  color: #fff !important;
}

.sider.ant-layout-sider .ant-menu-dark .ant-menu-item:hover,
.sider.ant-layout-sider .ant-menu-dark .ant-menu-submenu-title:hover {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Adjust icon and text alignment within menu items when collapsed */
.sider.ant-layout-sider .ant-menu-inline-collapsed > .ant-menu-item .anticon,
.sider.ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-item-group
  > .ant-menu-item-group-list
  > .ant-menu-item
  .anticon,
.sider.ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-submenu
  > .ant-menu-submenu-title
  .anticon {
  margin-right: 0 !important;
  transition: margin-right 0.2s;
}

.sider.ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-item
  .ant-menu-item-icon,
.sider.ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-item
  .ant-menu-item-icon {
  margin-right: 0 !important;
  transition: margin-right 0.2s;
}

.sider.ant-layout-sider .ant-menu-inline-collapsed .ant-menu-item-only-icon,
.sider .ant-layout-sider .ant-menu-inline-collapsed .ant-menu-item-only-icon {
  margin-right: 0 !important;
}

.sider.ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-item
  .ant-menu-title-content,
.sider
  .ant-layout-sider
  .ant-menu-inline-collapsed
  > .ant-menu-item
  .ant-menu-title-content {
  display: none !important;
}

/* Ensure proper spacing for content based on sidebar state */
/* These might need further adjustment depending on how the sidebar width is handled */
.siteLayoutCollapsed .header {
  /* padding-left handled by layout margin-left */
}

.siteLayoutExpanded .header {
  /* padding-left handled by layout margin-left */
}

.siteLayoutCollapsed .content {
  margin-left: 0;
}

.siteLayoutExpanded .content {
  margin-left: 0;
}
