[{"ContainingType": "Sever.Controllers.AdminController", "Method": "CreateAccounts", "RelativePath": "api/admin/create-accounts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "users", "Type": "System.Collections.Generic.List`1[[Sever.DTO.User.CreateUserRequest, Sever, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "DeleteAccount", "RelativePath": "api/admin/delete-user", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "Sever.DTO.User.DeleteUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetManagerInfo", "RelativePath": "api/admin/get-admin-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetUsersFromFile", "RelativePath": "api/admin/get-users-from-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdatemanagerAccount", "RelativePath": "api/admin/update-admin-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userRequest", "Type": "Sever.DTO.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdateSchoolInfo", "RelativePath": "api/admin/update-school-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "SchoolID", "Type": "System.String", "IsRequired": false}, {"Name": "Logo", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Address", "Type": "System.String", "IsRequired": false}, {"Name": "Hotline", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdateUserAccount", "RelativePath": "api/admin/update-user-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userRequest", "Type": "Sever.DTO.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "GoogleLogin", "RelativePath": "api/auth/google-login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "google", "Type": "Sever.DTO.Authentication.GoogleLoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "Refresh", "RelativePath": "api/auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tokenRequest", "Type": "Sever.DTO.Authentication.TokenResponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "GetManagerInfo", "RelativePath": "api/manager/get-manager-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "SearchStudentProfile", "RelativePath": "api/manager/search-student-profile", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "info", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "UpdatemanagerAccount", "RelativePath": "api/manager/update-manager-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userRequest", "Type": "Sever.DTO.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateMedicalEvent", "RelativePath": "api/nurse/event/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ActionTaken", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "EventType", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "ParentID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateMedicalEvent", "RelativePath": "api/nurse/event/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicalEventID", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ActionTaken", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "EventType", "Type": "System.String", "IsRequired": false}, {"Name": "ParentID", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateMedicine", "RelativePath": "api/nurse/medicine/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "SentDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateMedicine", "RelativePath": "api/nurse/medicine/update/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "SentDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetMedicalEventHistory", "RelativePath": "api/parent/medical-event/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "CreateMedicine", "RelativePath": "api/parent/medicine/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.SendMedicine.CreateMedicine", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "UpdateMedicine", "RelativePath": "api/parent/medicine/update/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Sever.DTO.SendMedicine.MedicineUpdateDTO", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.SchoolController", "Method": "GetSchoolInfo", "RelativePath": "api/school/get-school-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ValuesController", "Method": "CheckToken", "RelativePath": "api/Values/check-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Sever.WeatherForecast, Sever, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]