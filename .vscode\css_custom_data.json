{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's base, components, utilities and variants styles into your CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#tailwind"}]}, {"name": "@apply", "description": "Use the @apply directive to inline any existing utility classes into your own custom CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}, {"name": "@responsive", "description": "The @responsive directive allows you to define custom responsive variants.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#responsive"}]}, {"name": "@screen", "description": "The @screen directive allows you to create media queries that reference your breakpoints by name.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#screen"}]}, {"name": "@variants", "description": "The @variants directive allows you to define custom variants.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#variants"}]}]}