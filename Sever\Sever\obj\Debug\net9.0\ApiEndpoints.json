[{"ContainingType": "Sever.Controllers.AdminController", "Method": "ActivateAccount", "RelativePath": "api/admin/active-account", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "CreateAccounts", "RelativePath": "api/admin/create-accounts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "users", "Type": "System.Collections.Generic.List`1[[Sever.DTO.User.CreateUserRequest, Sever, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "CreateListStudent", "RelativePath": "api/admin/create-list-student", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "students", "Type": "System.Collections.Generic.List`1[[Sever.DTO.Student.CreateStudentRequest, Sever, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "CreateStudentProfile", "RelativePath": "api/admin/create-student-profile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "createStudentRequest", "Type": "Sever.DTO.Student.CreateStudentRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "DeleteStudentProfile", "RelativePath": "api/admin/delete-student-profile/{studentId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "DeleteAccount", "RelativePath": "api/admin/delete-user", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "Sever.DTO.User.DeleteUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "Get<PERSON>llAccount", "RelativePath": "api/admin/get-all-account", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetStudentInfoByParent", "RelativePath": "api/admin/get-student-info-by-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetStudentInfoByParentID", "RelativePath": "api/admin/get-student-info-by-parentID/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetStudentsFromFile", "RelativePath": "api/admin/get-students-from-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "GetUsersFromFile", "RelativePath": "api/admin/get-users-from-file", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "SearchUser", "RelativePath": "api/admin/search-user/{key}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "key", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdateSchoolInfo", "RelativePath": "api/admin/update-school-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "SchoolID", "Type": "System.String", "IsRequired": false}, {"Name": "Logo", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "LogoGifs", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "Name", "Type": "System.String", "IsRequired": false}, {"Name": "Address", "Type": "System.String", "IsRequired": false}, {"Name": "Hotline", "Type": "System.String", "IsRequired": false}, {"Name": "Email", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdateStudentProfile", "RelativePath": "api/admin/update-student-profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "StudentName", "Type": "System.String", "IsRequired": false}, {"Name": "Class", "Type": "System.String", "IsRequired": false}, {"Name": "StudentAvata", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "RelationName", "Type": "System.String", "IsRequired": false}, {"Name": "Nationality", "Type": "System.String", "IsRequired": false}, {"Name": "Ethnicity", "Type": "System.String", "IsRequired": false}, {"Name": "Birthday", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "Location", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AdminController", "Method": "UpdateUserAccount", "RelativePath": "api/admin/update-user-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userRequest", "Type": "Sever.DTO.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "ChangePassword", "RelativePath": "api/auth/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordRequest", "Type": "Sever.DTO.Authentication.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "ForgotPassword", "RelativePath": "api/auth/forgot-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.ForgotPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "GoogleLogin", "RelativePath": "api/auth/google-login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "google", "Type": "Sever.DTO.Authentication.GoogleLoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshToken", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "Refresh", "RelativePath": "api/auth/refresh", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "tokenRequest", "Type": "Sever.DTO.Authentication.TokenResponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Sever.DTO.Authentication.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "CreateNews", "RelativePath": "api/manager/create-news", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "Summary", "Type": "System.String", "IsRequired": false}, {"Name": "Body", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "DeleteNews", "RelativePath": "api/manager/delete-news/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "GenerateReport", "RelativePath": "api/manager/generate-report", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "GetAllStudent", "RelativePath": "api/manager/get-all-student", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "GetNewsByManager", "RelativePath": "api/manager/get-news-by-manager", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "SearchStudentProfile", "RelativePath": "api/manager/search-student-profile/{info}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "info", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ManagerController", "Method": "UpdateNews", "RelativePath": "api/manager/update-news", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "NewsID", "Type": "System.String", "IsRequired": false}, {"Name": "Title", "Type": "System.String", "IsRequired": false}, {"Name": "Summary", "Type": "System.String", "IsRequired": false}, {"Name": "Body", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "System.Collections.Generic.List`1[[Microsoft.AspNetCore.Http.IFormFile, Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NewsController", "Method": "GetAllNews", "RelativePath": "api/news/get-all-news", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateAppointment", "RelativePath": "api/nurse/create-appointment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Appointment.CreateAppointment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateHealthCheckUpByClass", "RelativePath": "api/nurse/create-health-check-up-by-class", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "classId", "Type": "System.String", "IsRequired": false}, {"Name": "dateCheckUp", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "AddImageMedicalEvent", "RelativePath": "api/nurse/event/addImages/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicalEventID", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ActionTaken", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "EventType", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateMedicalEvent", "RelativePath": "api/nurse/event/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ActionTaken", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "EventType", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllMedicalEvent", "RelativePath": "api/nurse/event/getAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetMedicalEventById", "RelativePath": "api/nurse/event/getByEventId/{medicalEventId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "medicalEventId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetMedicalEventsByStudentId", "RelativePath": "api/nurse/event/getByStudentId/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateMedicalEvent", "RelativePath": "api/nurse/event/update/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicalEventID", "Type": "System.String", "IsRequired": false}, {"Name": "Description", "Type": "System.String", "IsRequired": false}, {"Name": "ActionTaken", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "EventType", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}, {"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllAppointmentAsync", "RelativePath": "api/nurse/get-all-appointment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllHealthCheckUp", "RelativePath": "api/nurse/get-all-health-check-up", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetHealthCheckupsByStudentId", "RelativePath": "api/nurse/get-health-check-up-by-student/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetHealthCheckupsByYear", "RelativePath": "api/nurse/get-health-check-up-by-year/{year}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetNotResponseHealthCheckUp", "RelativePath": "api/nurse/get-not-response-health-check-up", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllHealthProfiles", "RelativePath": "api/nurse/healthProfile/getAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetHealthProfile", "RelativePath": "api/nurse/healthProfile/getByStudent/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateHealthProfile", "RelativePath": "api/nurse/healthProfile/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "Sever.DTO.HealthProfile.UpdateHealthProfile", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "AddImage", "RelativePath": "api/nurse/medicine/addImage/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateMedicine", "RelativePath": "api/nurse/medicine/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllMedicines", "RelativePath": "api/nurse/medicine/getAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetMedicinesByStudentID", "RelativePath": "api/nurse/medicine/getByStudentId/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateMedicine", "RelativePath": "api/nurse/medicine/update/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateAppointment", "RelativePath": "api/nurse/update-appointment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Appointment.UpdateAppointment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateHealthCheckUp", "RelativePath": "api/nurse/update-health-check-up", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.HealthCheckUp.UpdateHealthCheckUp", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateWaitingStatusHealthCheckUp", "RelativePath": "api/nurse/update-waiting-status-health-check-up/{Id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateVaccinationByClassID", "RelativePath": "api/nurse/vaccine/createByClassID", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "VaccineID", "Type": "System.Int32", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "VaccinatedAt", "Type": "System.DateTime", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "ClassID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "CreateVaccination", "RelativePath": "api/nurse/vaccine/createByStudentID", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "VaccineID", "Type": "System.Int32", "IsRequired": false}, {"Name": "<PERSON><PERSON>", "Type": "System.Int32", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "VaccinatedAt", "Type": "System.DateTime", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "ClassID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetAllVaccine", "RelativePath": "api/nurse/vaccine/getAll", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetVaccineByStudentId", "RelativePath": "api/nurse/vaccine/getByStudentId/{studentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "studentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetConfirmVaccine", "RelativePath": "api/nurse/vaccine/getConfirm", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetDeniedVaccine", "RelativePath": "api/nurse/vaccine/getDenied", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "GetNotResponseVaccine", "RelativePath": "api/nurse/vaccine/getNotResponse", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateAfterVaccination", "RelativePath": "api/nurse/vaccine/updateAfterByRecordID/{recordId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "recordId", "Type": "System.String", "IsRequired": true}, {"Name": "DateTime", "Type": "System.DateTime", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "FollowUpNotes", "Type": "System.String", "IsRequired": false}, {"Name": "FollowUpDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.NurseController", "Method": "UpdateVaccination", "RelativePath": "api/nurse/vaccine/updateByRecordID/{recordId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "recordId", "Type": "System.String", "IsRequired": true}, {"Name": "<PERSON><PERSON>", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "VaccinatedAt", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "VaccineID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "VaccinatorID", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "ConfirmAppointment", "RelativePath": "api/parent/confirm-appointment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Appointment.UpdateAppointment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "ConfirmHealthCheckUp", "RelativePath": "api/parent/confirm-health-check-up", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.HealthCheckUp.HealthCheckUpResponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "DeniedAppointment", "RelativePath": "api/parent/denied-appointment", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Appointment.UpdateAppointment", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "DeniedHealthCheckUp", "RelativePath": "api/parent/denied-health-check-up", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.HealthCheckUp.HealthCheckUpResponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetMedicalEventsByParentID", "RelativePath": "api/parent/event/getByParentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetMedicalEventByStudentID", "RelativePath": "api/parent/event/getByStudentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetHealthCheckupsByParentId", "RelativePath": "api/parent/get-all-health-check-up-by-parent/{parentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "parentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetAppointmentByStudentId", "RelativePath": "api/parent/get-appointment-by-student-id/{StudentID}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StudentID", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetStudentInfoByParent", "RelativePath": "api/parent/get-student-info-by-parent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "DeclareHealthProfile", "RelativePath": "api/parent/healthProfile/declare", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "declareDto", "Type": "Sever.DTO.HealProfile.DeclareHealthProfile", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetHeathProfilesByParentID", "RelativePath": "api/parent/healthProfile/getByParentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "AddImage", "RelativePath": "api/parent/medicine/addImage", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "CreateMedicine", "RelativePath": "api/parent/medicine/create", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetMedicinesByParentID", "RelativePath": "api/parent/medicine/getByParentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetMedicinesByStudentID", "RelativePath": "api/parent/medicine/getByStudentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "UpdateMedicine", "RelativePath": "api/parent/medicine/update", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "MedicineName", "Type": "System.String", "IsRequired": false}, {"Name": "Quantity", "Type": "System.String", "IsRequired": false}, {"Name": "Dosage", "Type": "System.String", "IsRequired": false}, {"Name": "Instructions", "Type": "System.String", "IsRequired": false}, {"Name": "Notes", "Type": "System.String", "IsRequired": false}, {"Name": "Image", "Type": "Microsoft.AspNetCore.Http.IFormFile[]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "UpdateStudentProfile", "RelativePath": "api/parent/update-student-profile", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "StudentID", "Type": "System.String", "IsRequired": false}, {"Name": "StudentName", "Type": "System.String", "IsRequired": false}, {"Name": "Class", "Type": "System.String", "IsRequired": false}, {"Name": "StudentAvata", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "RelationName", "Type": "System.String", "IsRequired": false}, {"Name": "Nationality", "Type": "System.String", "IsRequired": false}, {"Name": "Ethnicity", "Type": "System.String", "IsRequired": false}, {"Name": "Birthday", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "Sex", "Type": "System.String", "IsRequired": false}, {"Name": "Location", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "ConfirmVaccine", "RelativePath": "api/parent/vaccine/confirm", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Vaccination.VaccineReponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "DeniedVaccine", "RelativePath": "api/parent/vaccine/denied", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Sever.DTO.Vaccination.VaccineReponse", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ParentController", "Method": "GetVaccineByParentId", "RelativePath": "api/parent/vaccine/getParentId", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.SchoolController", "Method": "GetAllNews", "RelativePath": "api/school/get-all-news", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.SchoolController", "Method": "GetSchoolInfo", "RelativePath": "api/school/get-school-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.UserController", "Method": "GetNotifyByUserId", "RelativePath": "api/user/get-notify-by-user-id", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.UserController", "Method": "GetUserInfo", "RelativePath": "api/user/get-user-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.UserController", "Method": "UpdatemanagerAccount", "RelativePath": "api/user/update-user-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userRequest", "Type": "Sever.DTO.User.UpdateUserRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.ValuesController", "Method": "CheckToken", "RelativePath": "api/Values/check-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Sever.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Sever.WeatherForecast, Sever, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]