* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  margin: 0 !important;
  padding: 0 !important;
}

#root {
  max-width: 100vw;
  margin: 0 !important;
  padding: 0 !important;
  text-align: left;
  background: none;
}

.layout-main {
  min-height: 100vh;
  background: #f6faff;
}

.site-sidebar {
  min-height: 100vh;
  background: #e3f2fd;
  border-right: 2px solid #b3e5fc;
}

.site-header {
  background: #fffde7;
  color: #ff9800;
  font-size: 1.3em;
  font-weight: bold;
  display: flex;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  box-shadow: 0 2px 8px #ffe08244;
}

.site-logo {
  height: 48px;
  margin-right: 16px;
}

.site-content {
  padding: 32px 24px;
  background: #f6faff;
  min-height: calc(100vh - 64px);
}

.card-cute {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px 0;
}

.icon-cute {
  font-size: 2.5em;
  color: #4caf50;
  margin-right: 12px;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

body {
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

h2 {
  color: #666;
  margin-bottom: 15px;
}
