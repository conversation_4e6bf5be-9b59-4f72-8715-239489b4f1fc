.sider,
.sider :global(.ant-layout-sider-children),
.sider :global(.ant-menu),
.sider :global(.ant-menu-dark),
.sider :global(.ant-menu-dark .ant-menu-item),
.sider :global(.ant-menu-dark .ant-menu-submenu-title) {
    background-color: #1890ff !important;
}

.sider {
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  transition: width 0.2s;
  box-shadow: 0 4px 12px rgba(0,0,0,0.5); /* bóng nhẹ */
}

.layout {
  min-height: 100vh;
}
.content {
  margin: 24px;
  min-height: 280px;
  background: #fff;
  border-radius: 4px;
  padding: 24px;
}

.contentWrapper {
  height: 100%;
}

.siteLayout {
  transition: margin-left 0.2s;
}

.siteLayoutCollapsed {
  margin-left: 80px; /* Width of collapsed sidebar */
}

.siteLayoutExpanded {
  margin-left: 200px; /* Width of expanded sidebar */
}

.logo {
  height: 64px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1890ff;
}


.logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}
.menu {
  border-right: none;
} 

.trigger {
  padding: 0 8px; /* Giảm padding để sát lề hơn */
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
  margin-left: -4px; /* Kéo về gần lề trái hơn */
}

.trigger:hover {
  color: #fafafa;
  opacity: 0.1;
}
.notificationButton {
  font-size: 20px;
  cursor: pointer;
  padding: 0 12px;
}
.userProfile{
  cursor: pointer;
  padding: 0 12px;
  transition: all 0.3s;
}
.userProfile:hover {
  background: rgba(0, 0, 0, 0.025);
}
.username {
  margin-left: 8px;
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
}
.header {
  padding: 0;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 1;
  height: 64px;
}
.headerLeft {
  display: flex;
  align-items: center;
  gap: 16px;
  padding-left: 4px;
}

.headerCenter {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.headerRight {
  display: flex;
  align-items: center;
  padding-right: 24px;
}

.logoContainer {
  height: 40px;
  display: flex;
  align-items: center;
}

.headerLogo {
  height: 100%;
  width: auto;
}

.searchBar {
  width: 300px;
}

.navLink {
  color: #666;
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  transition: all 0.3s ease;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 8px;
}

.navLink:hover {
  color: #1890ff;
  background: rgba(22, 82, 162, 0.05);
}

.navLink .anticon {
  font-size: 16px;
}

/* Ensure proper spacing for content based on sidebar state */
.siteLayoutCollapsed .header {
  padding-left: 80px;
}

.siteLayoutExpanded .header {
  padding-left: 200px;
}

.siteLayoutCollapsed .content {
  margin-left: 0;
}

.siteLayoutExpanded .content {
  margin-left: 0;
}

.notificationItem {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationItem:hover {
  background-color: #f8f9fa;
}

.notificationTitle {
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 4px;
  font-size: 14px;
}

.notificationDescription {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
  line-height: 1.5;
}

.notificationTime {
  color: #999;
  font-size: 12px;
}




