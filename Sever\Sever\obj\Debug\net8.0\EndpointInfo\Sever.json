{"openapi": "3.0.1", "info": {"title": "Sever", "version": "1.0"}, "paths": {"/api/admin/get-admin-info": {"get": {"tags": ["Admin"], "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-admin-info": {"put": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/create-accounts": {"post": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserRequest"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-user-info": {"put": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/delete-user": {"delete": {"tags": ["Admin"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeleteUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/get-users-from-file": {"post": {"tags": ["Admin"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/admin/update-school-info": {"put": {"tags": ["Admin"], "parameters": [{"name": "SchoolID", "in": "query", "schema": {"type": "string"}}, {"name": "Name", "in": "query", "schema": {"type": "string"}}, {"name": "Address", "in": "query", "schema": {"type": "string"}}, {"name": "Hotline", "in": "query", "schema": {"type": "string"}}, {"name": "Email", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Logo": {"type": "string", "format": "binary"}}}, "encoding": {"Logo": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/refresh": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/google-login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GoogleLoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/manager/get-manager-info": {"get": {"tags": ["Manager"], "responses": {"200": {"description": "OK"}}}}, "/api/manager/update-manager-info": {"put": {"tags": ["Manager"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/manager/search-student-profile": {"get": {"tags": ["Manager"], "requestBody": {"content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/create": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["Dosage", "MedicineName", "Quantity"], "type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "SentDate": {"type": "string", "format": "date-time"}, "Notes": {"type": "string"}, "Status": {"type": "string"}, "StudentID": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "SentDate": {"style": "form"}, "Notes": {"style": "form"}, "Status": {"style": "form"}, "StudentID": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/medicine/update/{id}": {"put": {"tags": ["Nurse"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicineName": {"type": "string"}, "Quantity": {"type": "string"}, "Dosage": {"type": "string"}, "Instructions": {"type": "string"}, "SentDate": {"type": "string", "format": "date-time"}, "Notes": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}, "Status": {"type": "string"}, "StudentID": {"type": "string"}}}, "encoding": {"MedicineName": {"style": "form"}, "Quantity": {"style": "form"}, "Dosage": {"style": "form"}, "Instructions": {"style": "form"}, "SentDate": {"style": "form"}, "Notes": {"style": "form"}, "Image": {"style": "form"}, "Status": {"style": "form"}, "StudentID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/create": {"post": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"Description": {"type": "string"}, "ActionTaken": {"type": "string"}, "Notes": {"type": "string"}, "EventType": {"type": "string"}, "StudentID": {"type": "array", "items": {"type": "string"}}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}, "ParentID": {"type": "string"}}}, "encoding": {"Description": {"style": "form"}, "ActionTaken": {"style": "form"}, "Notes": {"style": "form"}, "EventType": {"style": "form"}, "StudentID": {"style": "form"}, "Image": {"style": "form"}, "ParentID": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/nurse/event/update": {"put": {"tags": ["Nurse"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"MedicalEventID": {"type": "string"}, "Description": {"type": "string"}, "ActionTaken": {"type": "string"}, "Notes": {"type": "string"}, "EventType": {"type": "string"}, "ParentID": {"type": "string"}, "Image": {"type": "array", "items": {"type": "string", "format": "binary"}}}}, "encoding": {"MedicalEventID": {"style": "form"}, "Description": {"style": "form"}, "ActionTaken": {"style": "form"}, "Notes": {"style": "form"}, "EventType": {"style": "form"}, "ParentID": {"style": "form"}, "Image": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/create": {"post": {"tags": ["Parent"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateMedicine"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateMedicine"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateMedicine"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medicine/update/{id}": {"put": {"tags": ["Parent"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MedicineUpdateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/MedicineUpdateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/MedicineUpdateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/parent/medical-event/history": {"get": {"tags": ["Parent"], "responses": {"200": {"description": "OK"}}}}, "/api/school/get-school-info": {"get": {"tags": ["School"], "responses": {"200": {"description": "OK"}}}}, "/api/Values/check-token": {"get": {"tags": ["Values"], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"CreateMedicine": {"required": ["dosage", "medicine<PERSON>ame", "quantity"], "type": "object", "properties": {"medicineName": {"minLength": 1, "type": "string"}, "quantity": {"minLength": 1, "type": "string"}, "dosage": {"minLength": 1, "type": "string"}, "instructions": {"type": "string", "nullable": true}, "sentDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "studentID": {"type": "string", "nullable": true}, "image": {"type": "array", "items": {"type": "string", "format": "binary"}, "nullable": true}}, "additionalProperties": false}, "CreateUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "roleID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "DeleteUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ForgotPasswordRequest": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GoogleLoginDto": {"type": "object", "properties": {"idToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MedicineUpdateDTO": {"type": "object", "properties": {"medicineName": {"type": "string", "nullable": true}, "quantity": {"type": "string", "nullable": true}, "dosage": {"type": "string", "nullable": true}, "instructions": {"type": "string", "nullable": true}, "sentDate": {"type": "string", "format": "date-time", "nullable": true}, "notes": {"type": "string", "nullable": true}, "image": {"type": "array", "items": {"type": "string", "format": "binary"}, "nullable": true}, "studentID": {"type": "string", "nullable": true}, "nurseID": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"token": {"type": "string", "nullable": true}, "newPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TokenResponse": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"userName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}