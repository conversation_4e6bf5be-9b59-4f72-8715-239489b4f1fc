import React from "react";
import { Layout, Menu } from "antd";
import { useLocation, useNavigate } from "react-router-dom";
import {
  UserOutlined,
  MedicineBoxOutlined,
  BarChartOutlined,
  SendOutlined,
  SettingOutlined,
  HeartOutlined,
  AlertOutlined,
} from "@ant-design/icons";

const { Sider } = Layout;

function SidebarParent({ collapsed }) {
  const navigate = useNavigate();
  const location = useLocation();

  const basePath = "/parent";

  const menuItems = [
    {
      key: `${basePath}/profile-student`,
      icon: <UserOutlined />,
      label: "<PERSON><PERSON> sơ học sinh",
    },
    {
      key: `${basePath}/declare-health`,
      icon: <HeartOutlined />,
      label: "<PERSON><PERSON> báo sức khỏe",
    },
    {
      key: `${basePath}/health-result`,
      icon: <BarChartOutlined />,
      label: "Khám sức khỏe",
    },
    {
      key: `${basePath}/medical-events`,
      icon: <AlertOutlined />,
      label: "Sự cố y tế",
    },
    {
      key: `${basePath}/vaccinations`,
      icon: <MedicineBoxOutlined />,
      label: "Tiêm chủng",
      children: [
        {
          key: `${basePath}/vaccinations/results`,
          label: "Kết quả tiêm chủng",
        },
        {
          key: `${basePath}/vaccinations/requirements`,
          label: "Yêu cầu tiêm chủng",
        },
      ],
    },
    {
      key: `${basePath}/send-medicine`,
      icon: <SendOutlined />,
      label: "Gửi thuốc",
    },
    {
      key: `${basePath}/profileParent`,
      icon: <UserOutlined />,
      label: "Hồ sơ cá nhân",
    },
    {
      key: `${basePath}/setting`,
      icon: <SettingOutlined />,
      label: "Cài đặt",
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  return (
    <Sider
      trigger={null}
      collapsible={true}
      collapsed={collapsed}
      collapsedWidth={80}
      width={200}
      className="fixed h-screen left-0 top-0 bottom-0 z-10 transition-all duration-200"
      theme="dark"
      style={{
        background: "linear-gradient(135deg, #47c8f8 0%, #1890ff 100%)",
      }}
    >
      {/* Vùng hiển thị logo bệnh viện */}
      <div className="logoContainer flex justify-center items-center h-24">
        <img
          src="/SchoolMedical.gif"
          alt="School Medical Logo"
          style={{
            height: collapsed ? "5500px" : "5500px",
            width: collapsed ? "auto" : "auto",
            maxWidth: "95%",
            objectFit: "contain",
            margin: "0 auto",
            display: "block",
          }}
        />
      </div>
      {/* Menu điều hướng chính */}
      <Menu
        theme="dark"
        mode="inline"
        inlineCollapsed={collapsed}
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        className="sidebar-menu border-none bg-transparent"
        style={{
          background: "transparent",
        }}
      />
    </Sider>
  );
}

export default SidebarParent;
