﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Sever.Context;

#nullable disable

namespace Sever.Migrations
{
    [DbContext(typeof(DataContext))]
    [Migration("20250714145257_dsfg")]
    partial class dsfg
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.6")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Sever.Model.Appointment", b =>
                {
                    b.Property<string>("AppointmentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("HealthCheckUpID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("AppointmentID");

                    b.HasIndex("HealthCheckUpID");

                    b.ToTable("Appointment");
                });

            modelBuilder.Entity("Sever.Model.Files", b =>
                {
                    b.Property<int>("FileID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("FileID"));

                    b.Property<byte[]>("FileData")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("FileLink")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("MedicalEventID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("MedicineID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("NewsID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("SchoolID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("StudentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("UploadDate")
                        .HasColumnType("datetime2");

                    b.HasKey("FileID");

                    b.HasIndex("MedicalEventID");

                    b.HasIndex("MedicineID");

                    b.HasIndex("NewsID");

                    b.HasIndex("SchoolID");

                    b.HasIndex("StudentID");

                    b.ToTable("Files");
                });

            modelBuilder.Entity("Sever.Model.ForgotPasswordToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsUsed")
                        .HasColumnType("bit");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("ForgotPasswordToken");
                });

            modelBuilder.Entity("Sever.Model.HealthCheckUp", b =>
                {
                    b.Property<string>("HealthCheckUpID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Ardiovascular")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("BMI")
                        .HasColumnType("real");

                    b.Property<float?>("BloodPressure")
                        .HasColumnType("real");

                    b.Property<DateTime?>("CheckDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CheckerID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Dental")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Hearing")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("Height")
                        .HasColumnType("real");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Respiration")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Skin")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<int?>("VisionLeft")
                        .HasColumnType("int");

                    b.Property<int?>("VisionRight")
                        .HasColumnType("int");

                    b.Property<float?>("Weight")
                        .HasColumnType("real");

                    b.HasKey("HealthCheckUpID");

                    b.HasIndex("CheckerID");

                    b.HasIndex("ParentID");

                    b.HasIndex("StudentID");

                    b.ToTable("HealthCheckUp");
                });

            modelBuilder.Entity("Sever.Model.HealthProfile", b =>
                {
                    b.Property<string>("HealthProfileID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("AllergyHistory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ChronicDiseases")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Disabilities")
                        .HasColumnType("nvarchar(max)");

                    b.Property<float?>("Height")
                        .HasColumnType("real");

                    b.Property<string>("OtheHealthIssues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<byte?>("PastSurgeries")
                        .HasColumnType("tinyint");

                    b.Property<string>("StudentID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("SurgicalCause")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ToothDecay")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int?>("VisionLeft")
                        .HasColumnType("int");

                    b.Property<int?>("VisionRight")
                        .HasColumnType("int");

                    b.Property<float?>("Weight")
                        .HasColumnType("real");

                    b.HasKey("HealthProfileID");

                    b.HasIndex("StudentID");

                    b.ToTable("HealthProfile");
                });

            modelBuilder.Entity("Sever.Model.MedicalEvent", b =>
                {
                    b.Property<string>("MedicalEventID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ActionTaken")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EventDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NurseID")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("MedicalEventID");

                    b.HasIndex("NurseID");

                    b.ToTable("MedicalEvent");
                });

            modelBuilder.Entity("Sever.Model.MedicalEventDetail", b =>
                {
                    b.Property<string>("MedicalEventID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("StudentID")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("MedicalEventID", "StudentID");

                    b.HasIndex("StudentID");

                    b.ToTable("MedicalEventDetail");
                });

            modelBuilder.Entity("Sever.Model.Medicine", b =>
                {
                    b.Property<string>("MedicineID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Dosage")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Instructions")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MedicineName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NurseID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ParentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Quantity")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("SentDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("MedicineID");

                    b.HasIndex("NurseID");

                    b.HasIndex("ParentID");

                    b.HasIndex("StudentID");

                    b.ToTable("Medicine");
                });

            modelBuilder.Entity("Sever.Model.News", b =>
                {
                    b.Property<string>("NewsID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("Status")
                        .HasColumnType("bit");

                    b.Property<string>("Summary")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("NewsID");

                    b.HasIndex("UserID");

                    b.ToTable("News");
                });

            modelBuilder.Entity("Sever.Model.Notify", b =>
                {
                    b.Property<string>("NotifyID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("date");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NotifyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Title")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserID")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("NotifyID");

                    b.HasIndex("UserID");

                    b.ToTable("Notify");
                });

            modelBuilder.Entity("Sever.Model.PolicyAndTerm", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SchoolID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("titile")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("SchoolID");

                    b.ToTable("PolicyAndTerm");
                });

            modelBuilder.Entity("Sever.Model.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("ExpiryDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshToken");
                });

            modelBuilder.Entity("Sever.Model.Role", b =>
                {
                    b.Property<string>("RoleID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("RoleID");

                    b.ToTable("Role");
                });

            modelBuilder.Entity("Sever.Model.SchoolInfo", b =>
                {
                    b.Property<string>("SchoolID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Hotline")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LogGifs")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Logo")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("SchoolID");

                    b.ToTable("SchoolInfo");
                });

            modelBuilder.Entity("Sever.Model.StudentProfile", b =>
                {
                    b.Property<string>("StudentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("Birthday")
                        .HasColumnType("datetime2");

                    b.Property<string>("Class")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ethnicity")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nationality")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ParentID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RelationName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Sex")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentAvata")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("StudentID");

                    b.HasIndex("ParentID");

                    b.ToTable("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.User", b =>
                {
                    b.Property<string>("UserID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserID");

                    b.HasIndex("RoleID");

                    b.HasIndex("UserName")
                        .IsUnique();

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Sever.Model.VaccinationRecord", b =>
                {
                    b.Property<string>("RecordID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Dose")
                        .HasColumnType("int");

                    b.Property<DateTime?>("FollowUpDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FollowUpNotes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NurseID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Status")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("StudentID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("VaccinatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("VaccinatorID")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("VaccineID")
                        .HasColumnType("int");

                    b.HasKey("RecordID");

                    b.HasIndex("NurseID");

                    b.HasIndex("StudentID");

                    b.HasIndex("VaccinatorID");

                    b.HasIndex("VaccineID");

                    b.ToTable("VaccinationRecord");
                });

            modelBuilder.Entity("Sever.Model.Vaccine", b =>
                {
                    b.Property<int>("VaccineID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("VaccineID"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserID")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("VaccineName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("VaccineID");

                    b.HasIndex("UserID");

                    b.ToTable("Vaccine");
                });

            modelBuilder.Entity("Sever.Model.Appointment", b =>
                {
                    b.HasOne("Sever.Model.HealthCheckUp", "HealthCheckUp")
                        .WithMany("Appointment")
                        .HasForeignKey("HealthCheckUpID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("HealthCheckUp");
                });

            modelBuilder.Entity("Sever.Model.Files", b =>
                {
                    b.HasOne("Sever.Model.MedicalEvent", "MedicalEvent")
                        .WithMany("File")
                        .HasForeignKey("MedicalEventID");

                    b.HasOne("Sever.Model.Medicine", "Medicine")
                        .WithMany("File")
                        .HasForeignKey("MedicineID");

                    b.HasOne("Sever.Model.News", "News")
                        .WithMany("File")
                        .HasForeignKey("NewsID");

                    b.HasOne("Sever.Model.SchoolInfo", "SchoolInfo")
                        .WithMany()
                        .HasForeignKey("SchoolID");

                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany()
                        .HasForeignKey("StudentID");

                    b.Navigation("MedicalEvent");

                    b.Navigation("Medicine");

                    b.Navigation("News");

                    b.Navigation("SchoolInfo");

                    b.Navigation("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.ForgotPasswordToken", b =>
                {
                    b.HasOne("Sever.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sever.Model.HealthCheckUp", b =>
                {
                    b.HasOne("Sever.Model.User", "Checker")
                        .WithMany("NurseHealthCheckUp")
                        .HasForeignKey("CheckerID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sever.Model.User", "Parent")
                        .WithMany("ParentHealthCheckUp")
                        .HasForeignKey("ParentID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany("HealthCheckUp")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Checker");

                    b.Navigation("Parent");

                    b.Navigation("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.HealthProfile", b =>
                {
                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany("HealthProfiles")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.MedicalEvent", b =>
                {
                    b.HasOne("Sever.Model.User", "Nurse")
                        .WithMany("MedicalEvent")
                        .HasForeignKey("NurseID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Nurse");
                });

            modelBuilder.Entity("Sever.Model.MedicalEventDetail", b =>
                {
                    b.HasOne("Sever.Model.MedicalEvent", "MedicalEvent")
                        .WithMany("MedicalEventDetail")
                        .HasForeignKey("MedicalEventID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany("MedicalEventDetail")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("MedicalEvent");

                    b.Navigation("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.Medicine", b =>
                {
                    b.HasOne("Sever.Model.User", "Nurse")
                        .WithMany()
                        .HasForeignKey("NurseID");

                    b.HasOne("Sever.Model.User", "Parent")
                        .WithMany("Medicine")
                        .HasForeignKey("ParentID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany()
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Nurse");

                    b.Navigation("Parent");

                    b.Navigation("StudentProfile");
                });

            modelBuilder.Entity("Sever.Model.News", b =>
                {
                    b.HasOne("Sever.Model.User", "User")
                        .WithMany("News")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sever.Model.Notify", b =>
                {
                    b.HasOne("Sever.Model.User", "User")
                        .WithMany("Notify")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sever.Model.PolicyAndTerm", b =>
                {
                    b.HasOne("Sever.Model.SchoolInfo", "SchoolInfo")
                        .WithMany("PolicyAndTerm")
                        .HasForeignKey("SchoolID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SchoolInfo");
                });

            modelBuilder.Entity("Sever.Model.RefreshToken", b =>
                {
                    b.HasOne("Sever.Model.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sever.Model.StudentProfile", b =>
                {
                    b.HasOne("Sever.Model.User", "Parent")
                        .WithMany("StudentProfile")
                        .HasForeignKey("ParentID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Parent");
                });

            modelBuilder.Entity("Sever.Model.User", b =>
                {
                    b.HasOne("Sever.Model.Role", "Role")
                        .WithMany("Users")
                        .HasForeignKey("RoleID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Sever.Model.VaccinationRecord", b =>
                {
                    b.HasOne("Sever.Model.User", "Nurse")
                        .WithMany("NurseVaccinationRecord")
                        .HasForeignKey("NurseID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sever.Model.StudentProfile", "StudentProfile")
                        .WithMany("VaccinationRecord")
                        .HasForeignKey("StudentID")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Sever.Model.User", "Vaccinator")
                        .WithMany("VaccinatorVaccinationRecord")
                        .HasForeignKey("VaccinatorID")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Sever.Model.Vaccine", "Vaccine")
                        .WithMany("VaccinationRecord")
                        .HasForeignKey("VaccineID")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Nurse");

                    b.Navigation("StudentProfile");

                    b.Navigation("Vaccinator");

                    b.Navigation("Vaccine");
                });

            modelBuilder.Entity("Sever.Model.Vaccine", b =>
                {
                    b.HasOne("Sever.Model.User", "User")
                        .WithMany("Vaccine")
                        .HasForeignKey("UserID")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Sever.Model.HealthCheckUp", b =>
                {
                    b.Navigation("Appointment");
                });

            modelBuilder.Entity("Sever.Model.MedicalEvent", b =>
                {
                    b.Navigation("File");

                    b.Navigation("MedicalEventDetail");
                });

            modelBuilder.Entity("Sever.Model.Medicine", b =>
                {
                    b.Navigation("File");
                });

            modelBuilder.Entity("Sever.Model.News", b =>
                {
                    b.Navigation("File");
                });

            modelBuilder.Entity("Sever.Model.Role", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("Sever.Model.SchoolInfo", b =>
                {
                    b.Navigation("PolicyAndTerm");
                });

            modelBuilder.Entity("Sever.Model.StudentProfile", b =>
                {
                    b.Navigation("HealthCheckUp");

                    b.Navigation("HealthProfiles");

                    b.Navigation("MedicalEventDetail");

                    b.Navigation("VaccinationRecord");
                });

            modelBuilder.Entity("Sever.Model.User", b =>
                {
                    b.Navigation("MedicalEvent");

                    b.Navigation("Medicine");

                    b.Navigation("News");

                    b.Navigation("Notify");

                    b.Navigation("NurseHealthCheckUp");

                    b.Navigation("NurseVaccinationRecord");

                    b.Navigation("ParentHealthCheckUp");

                    b.Navigation("StudentProfile");

                    b.Navigation("VaccinatorVaccinationRecord");

                    b.Navigation("Vaccine");
                });

            modelBuilder.Entity("Sever.Model.Vaccine", b =>
                {
                    b.Navigation("VaccinationRecord");
                });
#pragma warning restore 612, 618
        }
    }
}
