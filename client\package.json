{"name": "school-medical-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/charts": "^2.4.0", "@ant-design/icons": "^5.6.1", "@ant-design/plots": "^2.5.0", "@react-oauth/google": "^0.12.2", "antd": "^5.25.4", "axios": "^1.9.0", "dayjs": "^1.11.13", "framer-motion": "^12.15.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.3", "styled-components": "^6.1.18"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.31", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}